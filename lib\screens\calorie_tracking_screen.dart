import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/food_entry.dart';
import '../services/database_service.dart';

class CalorieTrackingScreen extends StatefulWidget {
  const CalorieTrackingScreen({super.key});

  @override
  State<CalorieTrackingScreen> createState() => _CalorieTrackingScreenState();
}

class _CalorieTrackingScreenState extends State<CalorieTrackingScreen> {
  int _dailyCalorieGoal = 2000;
  int _consumedCalories = 0;
  List<FoodEntry> _foodEntries = [];
  final DatabaseService _databaseService = DatabaseService();
  final DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    final goal = await _databaseService.getDailyCalorieGoal();
    final entries = await _databaseService.getFoodEntriesForDate(_selectedDate);

    setState(() {
      _dailyCalorieGoal = goal;
      _foodEntries = entries;
      _consumedCalories = entries.fold(0, (sum, entry) => sum + entry.calories);
    });
  }

  Future<void> _addFoodEntry() async {
    showDialog(
      context: context,
      builder: (context) => _AddFoodDialog(
        onAdd: (String food, int calories) async {
          final entry = FoodEntry(
            food: food,
            calories: calories,
            time: DateTime.now(),
          );

          await _databaseService.insertFoodEntry(entry);
          await _loadData(); // Reload data to update UI
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final remainingCalories = _dailyCalorieGoal - _consumedCalories;
    final progress = _consumedCalories / _dailyCalorieGoal;

    return Scaffold(
      appBar: AppBar(
        title: const Text("Nonni's Nutrition Tracker"),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showSettingsDialog,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Daily Summary Card
            Container(
              margin: const EdgeInsets.all(16),
              child: Card(
                elevation: 6,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                        Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.favorite,
                              color: Theme.of(context).colorScheme.tertiary,
                              size: 24,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              "Nonni's Daily Progress",
                              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          DateFormat('EEEE, MMM dd, yyyy').format(DateTime.now()),
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 20),
                        Container(
                          height: 8,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4),
                            color: Colors.grey[300],
                          ),
                          child: FractionallySizedBox(
                            alignment: Alignment.centerLeft,
                            widthFactor: progress.clamp(0.0, 1.0),
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(4),
                                gradient: LinearGradient(
                                  colors: progress > 1.0
                                    ? [Colors.red[400]!, Colors.red[600]!]
                                    : [Theme.of(context).colorScheme.primary, Theme.of(context).colorScheme.secondary],
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 20),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            _buildCalorieInfo('Consumed', _consumedCalories, Theme.of(context).colorScheme.secondary),
                            _buildCalorieInfo('Remaining', remainingCalories,
                              remainingCalories >= 0 ? Theme.of(context).colorScheme.primary : Colors.red),
                            _buildCalorieInfo('Goal', _dailyCalorieGoal, Colors.blue[600]!),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            // Food Entries List
            Expanded(
              child: _foodEntries.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.restaurant_menu,
                            size: 80,
                            color: Colors.grey[300],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Hey Nonni! 👋',
                            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Ready to start tracking your nutrition?\nTap the "Add Food" button to log your first meal!',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                              height: 1.4,
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      itemCount: _foodEntries.length,
                      itemBuilder: (context, index) {
                        final entry = _foodEntries[index];
                        return Card(
                          elevation: 2,
                          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                          child: ListTile(
                            leading: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                Icons.restaurant_menu,
                                color: Theme.of(context).colorScheme.secondary,
                              ),
                            ),
                            title: Text(
                              entry.food,
                              style: const TextStyle(fontWeight: FontWeight.w600),
                            ),
                            subtitle: Text(
                              DateFormat('HH:mm').format(entry.time),
                              style: TextStyle(color: Colors.grey[600]),
                            ),
                            trailing: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Text(
                                '${entry.calories} cal',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addFoodEntry,
        icon: const Icon(Icons.add),
        label: const Text('Add Food'),
        backgroundColor: Theme.of(context).colorScheme.secondary,
        foregroundColor: Colors.white,
      ),
    );
  }

  void _showSettingsDialog() {
    final goalController = TextEditingController(text: _dailyCalorieGoal.toString());

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text("Nonni's Daily Goal"),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Set your daily calorie goal:'),
            const SizedBox(height: 16),
            TextField(
              controller: goalController,
              decoration: const InputDecoration(
                labelText: 'Daily Calorie Goal',
                suffixText: 'calories',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final newGoal = int.tryParse(goalController.text) ?? _dailyCalorieGoal;
              await _databaseService.setDailyCalorieGoal(newGoal);
              setState(() {
                _dailyCalorieGoal = newGoal;
              });
              if (mounted) {
                Navigator.of(context).pop();
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  Widget _buildCalorieInfo(String label, int value, Color color) {
    return Column(
      children: [
        Text(
          value.toString(),
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Colors.grey),
        ),
      ],
    );
  }
}

class _AddFoodDialog extends StatefulWidget {
  final Function(String, int) onAdd;

  const _AddFoodDialog({required this.onAdd});

  @override
  State<_AddFoodDialog> createState() => _AddFoodDialogState();
}

class _AddFoodDialogState extends State<_AddFoodDialog> {
  final _foodController = TextEditingController();
  final _caloriesController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      title: Row(
        children: [
          Icon(
            Icons.restaurant_menu,
            color: Theme.of(context).colorScheme.secondary,
          ),
          const SizedBox(width: 8),
          const Text('Add Food Entry'),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _foodController,
            decoration: InputDecoration(
              labelText: 'Food/Meal',
              hintText: 'e.g., Chicken Salad',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              prefixIcon: const Icon(Icons.fastfood),
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _caloriesController,
            decoration: InputDecoration(
              labelText: 'Calories',
              hintText: 'e.g., 350',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              prefixIcon: const Icon(Icons.local_fire_department),
              suffixText: 'cal',
            ),
            keyboardType: TextInputType.number,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            final food = _foodController.text.trim();
            final calories = int.tryParse(_caloriesController.text) ?? 0;

            if (food.isNotEmpty && calories > 0) {
              widget.onAdd(food, calories);
              Navigator.of(context).pop();
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.secondary,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
          child: const Text('Add'),
        ),
      ],
    );
  }
}
